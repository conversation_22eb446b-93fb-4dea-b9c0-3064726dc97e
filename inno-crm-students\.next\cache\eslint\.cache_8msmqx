[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\app\\dashboard\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts": "13"}, {"size": 2713, "mtime": 1750434013788, "results": "14", "hashOfConfig": "15"}, {"size": 1037, "mtime": 1750433999424, "results": "16", "hashOfConfig": "15"}, {"size": 1579, "mtime": 1750433884600, "results": "17", "hashOfConfig": "15"}, {"size": 271, "mtime": 1750433874619, "results": "18", "hashOfConfig": "15"}, {"size": 166, "mtime": 1750433890252, "results": "19", "hashOfConfig": "15"}, {"size": 2713, "mtime": 1750435194033, "results": "20", "hashOfConfig": "15"}, {"size": 689, "mtime": 1750361517867, "results": "21", "hashOfConfig": "15"}, {"size": 104, "mtime": 1750434115787, "results": "22", "hashOfConfig": "15"}, {"size": 1037, "mtime": 1750435154954, "results": "23", "hashOfConfig": "15"}, {"size": 1725, "mtime": 1750436048518, "results": "24", "hashOfConfig": "15"}, {"size": 271, "mtime": 1750435161898, "results": "25", "hashOfConfig": "15"}, {"size": 166, "mtime": 1750435179070, "results": "26", "hashOfConfig": "15"}, {"size": 374, "mtime": 1750435818442, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19o6u7n", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts", [], []]