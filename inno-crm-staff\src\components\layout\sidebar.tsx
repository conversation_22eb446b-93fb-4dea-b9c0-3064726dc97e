"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { 
  Users, 
  GraduationCap, 
  DollarSign, 
  Calendar, 
  Phone, 
  Settings,
  BarChart3
} from "lucide-react"

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: BarChart3 },
  { name: "Students", href: "/dashboard/students", icon: GraduationCap },
  { name: "Teachers", href: "/dashboard/teachers", icon: Users },
  { name: "Groups", href: "/dashboard/groups", icon: Calendar },
  { name: "Leads", href: "/dashboard/leads", icon: Phone },
  { name: "Payments", href: "/dashboard/payments", icon: DollarSign },
  { name: "Settings", href: "/dashboard/settings", icon: Settings },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="drawer-side">
      <label htmlFor="drawer-toggle" className="drawer-overlay"></label>
      <aside className="min-h-full w-64 bg-base-200">
        <div className="p-4">
          <h1 className="text-xl font-bold text-primary">
            Staff Portal
          </h1>
        </div>
        <ul className="menu p-4 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href
            
            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-content"
                      : "text-base-content hover:bg-base-300"
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </Link>
              </li>
            )
          })}
        </ul>
      </aside>
    </div>
  )
}
