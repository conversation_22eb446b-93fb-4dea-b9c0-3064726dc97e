import type { NextAuthOptions } from 'next-auth'
import type { JWT } from 'next-auth/jwt'
import type { Session } from 'next-auth'

// Basic auth configuration for deployment
export const authOptions: NextAuthOptions = {
  providers: [],
  session: {
    strategy: 'jwt' as const
  },
  callbacks: {
    jwt: async ({ token }: { token: JWT }) => {
      return token
    },
    session: async ({ session }: { session: Session }) => {
      return session
    }
  }
}
